package com.raindrop.manga_service.service;

import com.raindrop.manga_service.dto.response.ApiResponse;
import com.raindrop.manga_service.dto.response.MangaSummaryResponse;
import com.raindrop.manga_service.dto.response.ReadingHistoryResponse;
import com.raindrop.manga_service.entity.Genre;
import com.raindrop.manga_service.entity.Manga;
import com.raindrop.manga_service.mapper.MangaMapper;
import com.raindrop.manga_service.repository.ChapterRepository;
import com.raindrop.manga_service.repository.MangaRepository;
import com.raindrop.manga_service.repository.httpclient.HistoryClient;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@Slf4j
public class RecommendationService {
    
    HistoryClient historyClient;
    MangaRepository mangaRepository;
    ChapterRepository chapterRepository;
    MangaMapper mangaMapper;
    
    /**
     * Lấy gợi ý manga dựa trên thể loại từ lịch sử đọc
     * @param userId ID của người dùng
     * @param limit Số lượng manga gợi ý (mặc định là 6)
     * @return Danh sách manga được gợi ý
     */
    public List<MangaSummaryResponse> getRecommendationsByGenreSummary(String userId, int limit) {
        log.info("Getting recommendations by genre for user: {}, limit: {}", userId, limit);
        
        try {
            // Bước 1: Lấy 3 truyện gần nhất từ lịch sử đọc
            List<ReadingHistoryResponse> recentHistory = getRecentReadingHistory(userId, 3);
            
            if (recentHistory.isEmpty()) {
                log.info("No reading history found for user {}, returning popular manga", userId);
                return getPopularMangaAsFallback(limit);
            }
            
            // Bước 2: Tính trọng số cho các thể loại
            Map<String, Double> genreWeights = calculateGenreWeights(recentHistory);
            log.info("Calculated genre weights for user {}: {}", userId, genreWeights);
            
            // Bước 3: Lấy danh sách manga ID đã đọc để loại trừ
            List<String> readMangaIds = getAllReadMangaIds(userId);
            log.info("Found {} read manga IDs for user {}", readMangaIds.size(), userId);
            
            // Bước 4: Lọc manga theo thể loại và loại trừ manga đã đọc
            List<Manga> recommendedMangas = findMangasByGenreWeights(genreWeights, readMangaIds, limit);
            
            // Bước 5: Chuyển đổi sang MangaSummaryResponse
            List<MangaSummaryResponse> result = recommendedMangas.stream()
                    .map(manga -> {
                        MangaSummaryResponse response = mangaMapper.toMangaSummaryResponse(manga);
                        
                        // Thêm thông tin lastChapterNumber nếu có
                        if (manga.getLastChapterId() != null) {
                            chapterRepository.findById(manga.getLastChapterId()).ifPresent(chapter -> {
                                response.setLastChapterNumber(chapter.getChapterNumber());
                            });
                        }
                        
                        return response;
                    })
                    .collect(Collectors.toList());
            
            log.info("Successfully generated {} recommendations for user {}", result.size(), userId);
            return result;
            
        } catch (Exception e) {
            log.error("Error generating recommendations for user {}: {}", userId, e.getMessage(), e);
            // Fallback: trả về manga phổ biến
            return getPopularMangaAsFallback(limit);
        }
    }
    
    /**
     * Lấy lịch sử đọc gần đây từ History Service
     */
    private List<ReadingHistoryResponse> getRecentReadingHistory(String userId, int limit) {
        try {
            log.info("Fetching recent reading history for user: {}, limit: {}", userId, limit);
            
            // Gọi History Service để lấy lịch sử đọc gần đây
            ApiResponse<List<ReadingHistoryResponse>> response = historyClient.getRecentReadingHistory(
                    "Bearer dummy-token", // Token sẽ được xử lý bởi security context
                    userId, 
                    limit
            );
            
            if (response != null && response.getCode() == 200 && response.getResult() != null) {
                log.info("Successfully fetched {} recent reading history items", response.getResult().size());
                return response.getResult();
            } else {
                log.warn("Failed to fetch reading history or empty result for user {}", userId);
                return Collections.emptyList();
            }
            
        } catch (Exception e) {
            log.error("Error fetching recent reading history for user {}: {}", userId, e.getMessage());
            return Collections.emptyList();
        }
    }
    
    /**
     * Lấy tất cả manga ID đã đọc từ History Service
     */
    private List<String> getAllReadMangaIds(String userId) {
        try {
            log.info("Fetching all read manga IDs for user: {}", userId);
            
            ApiResponse<List<String>> response = historyClient.getAllReadMangaIds(
                    "Bearer dummy-token", // Token sẽ được xử lý bởi security context
                    userId
            );
            
            if (response != null && response.getCode() == 200 && response.getResult() != null) {
                log.info("Successfully fetched {} read manga IDs", response.getResult().size());
                return response.getResult();
            } else {
                log.warn("Failed to fetch read manga IDs or empty result for user {}", userId);
                return Collections.emptyList();
            }
            
        } catch (Exception e) {
            log.error("Error fetching read manga IDs for user {}: {}", userId, e.getMessage());
            return Collections.emptyList();
        }
    }
    
    /**
     * Tính trọng số cho các thể loại dựa trên lịch sử đọc
     * Truyện đọc gần nhất sẽ có trọng số cao hơn
     */
    private Map<String, Double> calculateGenreWeights(List<ReadingHistoryResponse> recentHistory) {
        Map<String, Double> genreWeights = new HashMap<>();
        
        for (int i = 0; i < recentHistory.size(); i++) {
            ReadingHistoryResponse history = recentHistory.get(i);
            
            // Tìm manga để lấy thông tin thể loại
            Optional<Manga> mangaOpt = mangaRepository.findByIdAndDeletedFalse(history.getMangaId());
            
            if (mangaOpt.isPresent()) {
                Manga manga = mangaOpt.get();
                
                // Trọng số giảm dần: truyện đầu tiên (gần nhất) có trọng số cao nhất
                double weight = 1.0 - (i * 0.2); // 1.0, 0.8, 0.6
                
                // Cộng trọng số cho từng thể loại của manga
                for (Genre genre : manga.getGenres()) {
                    genreWeights.merge(genre.getName(), weight, Double::sum);
                }
                
                log.debug("Added weight {} for manga {} with genres: {}", 
                         weight, manga.getTitle(), 
                         manga.getGenres().stream().map(Genre::getName).collect(Collectors.toList()));
            }
        }
        
        return genreWeights;
    }
    
    /**
     * Tìm manga dựa trên trọng số thể loại
     */
    private List<Manga> findMangasByGenreWeights(Map<String, Double> genreWeights, 
                                                 List<String> excludeMangaIds, 
                                                 int limit) {
        
        if (genreWeights.isEmpty()) {
            log.warn("No genre weights available, falling back to popular manga");
            return getPopularMangaList(excludeMangaIds, limit);
        }
        
        // Sắp xếp thể loại theo trọng số giảm dần
        List<String> sortedGenres = genreWeights.entrySet().stream()
                .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        
        log.info("Sorted genres by weight: {}", sortedGenres);
        
        // Đảm bảo excludeMangaIds không null và không rỗng
        List<String> safeExcludeMangaIds = excludeMangaIds != null && !excludeMangaIds.isEmpty() 
                ? excludeMangaIds 
                : Collections.singletonList("dummy-id-to-avoid-empty-list");
        
        // Tìm manga theo thể loại với trọng số cao nhất
        List<Manga> recommendedMangas = mangaRepository.findMangasByGenres(
                sortedGenres, 
                safeExcludeMangaIds, 
                PageRequest.of(0, limit * 2) // Lấy nhiều hơn để có đủ sau khi lọc
        );
        
        // Giới hạn kết quả
        return recommendedMangas.stream()
                .limit(limit)
                .collect(Collectors.toList());
    }
    
    /**
     * Fallback: Lấy manga phổ biến khi không có lịch sử đọc
     */
    private List<MangaSummaryResponse> getPopularMangaAsFallback(int limit) {
        log.info("Using popular manga as fallback, limit: {}", limit);
        
        List<Manga> popularMangas = mangaRepository.findByOrderByViewsDesc(PageRequest.of(0, limit));
        
        return popularMangas.stream()
                .map(manga -> {
                    MangaSummaryResponse response = mangaMapper.toMangaSummaryResponse(manga);
                    
                    // Thêm thông tin lastChapterNumber nếu có
                    if (manga.getLastChapterId() != null) {
                        chapterRepository.findById(manga.getLastChapterId()).ifPresent(chapter -> {
                            response.setLastChapterNumber(chapter.getChapterNumber());
                        });
                    }
                    
                    return response;
                })
                .collect(Collectors.toList());
    }
    
    /**
     * Lấy danh sách manga phổ biến (dạng entity)
     */
    private List<Manga> getPopularMangaList(List<String> excludeMangaIds, int limit) {
        // Nếu không có manga nào để loại trừ, lấy manga phổ biến trực tiếp
        if (excludeMangaIds == null || excludeMangaIds.isEmpty()) {
            return mangaRepository.findByOrderByViewsDesc(PageRequest.of(0, limit));
        }
        
        // Lấy manga phổ biến và lọc bỏ những manga đã đọc
        List<Manga> allPopular = mangaRepository.findByOrderByViewsDesc(PageRequest.of(0, limit * 2));
        
        return allPopular.stream()
                .filter(manga -> !excludeMangaIds.contains(manga.getId()))
                .limit(limit)
                .collect(Collectors.toList());
    }
}
